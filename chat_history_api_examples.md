# Chat History API - Updated with Count Parameter

## Overview

The `/chat_history` endpoint has been updated to support an optional `count` parameter that allows you to limit the number of chat history records returned.

## API Endpoint

```
GET /chat_history
```

## Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `from_id` | int | Yes | The ID of the sender |
| `chat_id` | int | Yes | The ID of the chat/recipient |
| `count` | int | No | Number of recent chat history records to retrieve. If not provided, all records are returned. |

## Usage Examples

### 1. Get All Chat History (Existing Behavior)

```bash
curl -X GET "http://localhost:8000/chat_history?from_id=123&chat_id=456" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

This maintains backward compatibility - existing API calls continue to work unchanged.

### 2. Get Last 10 Chat History Records

```bash
curl -X GET "http://localhost:8000/chat_history?from_id=123&chat_id=456&count=10" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

This returns only the 10 most recent chat history records.

### 3. Get Last 5 Chat History Records

```bash
curl -X GET "http://localhost:8000/chat_history?from_id=123&chat_id=456&count=5" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

This returns only the 5 most recent chat history records.

## Response Format

The response format remains the same:

```json
{
  "chat_history": [
    ["Chat History:\n\n[message content]\n"]
  ]
}
```

## Implementation Details

### Caching Behavior

- Cache keys now include the `count` parameter to ensure correct caching behavior
- Different `count` values are cached separately
- Cache TTL remains 30 minutes (1800 seconds)

### SQL Query Changes

When `count` is specified:
- Uses a subquery with `LIMIT` to get the last N messages
- Orders by timestamp DESC to get the most recent messages first

When `count` is not specified (default):
- Uses the original query to maintain existing behavior
- Returns all chat history records

### Backward Compatibility

- Existing API calls without the `count` parameter continue to work unchanged
- No breaking changes to the response format
- Cache invalidation works correctly for both old and new cache keys

## Performance Considerations

- Using the `count` parameter can improve performance for large chat histories
- Smaller result sets reduce memory usage and network transfer time
- Caching ensures repeated requests with the same parameters are served quickly
